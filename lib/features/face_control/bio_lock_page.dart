// ignore_for_file: depend_on_referenced_packages

import 'package:click_bazaar/core/services/logout_service.dart';
import 'package:click_bazaar/features/auth/presentation/pages/login_page.dart';
import 'package:click_bazaar/features/camera/image_picker_utils.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:facesdk_plugin/facesdk_plugin.dart';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:isar/isar.dart';
import 'package:lottie/lottie.dart';
import 'package:path/path.dart' hide Context;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sqflite/sqflite.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_storage/get_storage.dart';
import 'package:click_bazaar/core/function/functions.dart';

// import 'package:click_bazaar/core/photo/image_picker_utils.dart';
import 'package:click_bazaar/core/widgets/custom_toast.dart';
import 'package:click_bazaar/features/auth/models/user_profile_model.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/core/utils/jwt_decoder.dart';
import 'package:click_bazaar/di/dependency_injection.dart';
import 'package:click_bazaar/core/extensions/context_extensions.dart';
import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:click_bazaar/generated/assets.dart';

import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';
import 'dart:io' show File, Platform;
import '../../env.dart';
import 'about.dart';
import 'settings.dart';
import 'person.dart';
import 'personview.dart';
import 'facedetectionview.dart';

// ignore: must_be_immutable

///TODO: Change DB to faster one / Optimize here
class BioLockPage extends StatefulWidget {
  BioLockPage({super.key});

  @override
  BioLockPageState createState() => BioLockPageState();
}

class BioLockPageState extends State<BioLockPage> {
  String _warningState = "";
  bool _visibleWarning = false;

  bool hasAnyUser = false;
  bool serverApproved = false;
  bool localApproved = false;
  bool _shouldRequestPermission = false;
  late AnimationController animationController;
  late AndroidDeviceInfo? androidInfo;
  late Future<dynamic> _initFuture;
  late final AppLifecycleListener _listener;
  final NetworkInfo networkInfo = di();
  final GetStorage storage = di();
  Dio dio = di();
  var personList = <Person>[];
  final ValueNotifier<bool> _listLoading = ValueNotifier<bool>(false);
  final _faceSdkPlugin = FacesdkPlugin();

  Future<Map<Permission, PermissionStatus>> requestPermissions() async {
    var verSDK = androidInfo?.version.sdkInt ?? 0;
    Map<Permission, PermissionStatus> statuses = await [
      Platform.isAndroid && verSDK < 33
          ? Permission.storage
          : Permission.photos,
      Permission.camera,
      Permission.location,
      Permission.notification
    ].request();

    return statuses;
  }

  Future<void> permissionWall() async {
    Map<Permission, PermissionStatus> statuses = {};
    statuses = await requestPermissions();
    var verSDK = androidInfo?.version.sdkInt ?? 0;

    if (statuses[Permission.camera] != PermissionStatus.granted ||
        statuses[Permission.location] != PermissionStatus.granted ||
        statuses[Permission.notification] != PermissionStatus.granted ||
        statuses[Platform.isAndroid && verSDK < 33
                ? Permission.storage
                : Permission.photos] !=
            PermissionStatus.granted) {
      if (statuses[Permission.camera] == PermissionStatus.permanentlyDenied ||
          statuses[Permission.location] == PermissionStatus.permanentlyDenied ||
          statuses[Permission.notification] ==
              PermissionStatus.permanentlyDenied ||
          statuses[Platform.isAndroid && verSDK < 33
                  ? Permission.storage
                  : Permission.photos] ==
              PermissionStatus.permanentlyDenied) {
        print(statuses[Permission.camera]);
        print(statuses[Permission.storage]);
        print(statuses[Permission.location]);
        print(statuses[Permission.notification]);

        ///Shows custom dialog after user refuses for giving of any permissions
        showCustomDialog(this.context);
      } else {
        ///Points to the recursion
        await permissionWall();
      }
    } else {
      await initBioLock();
    }
  }

  @override
  void initState() {
    if (Platform.isAndroid) {
      androidInfo = di();
    } else {
      androidInfo = null;
    }

    _listener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );

    reInitializerButton();

    super.initState();
  }

  @override
  void dispose() {
    _listener.dispose();
    super.dispose();
  }

  // Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
        break;
      case AppLifecycleState.resumed:
        _onResumed();
        break;
      case AppLifecycleState.inactive:
        _onInactive();
        break;
      case AppLifecycleState.hidden:
        _onHidden();
        break;
      case AppLifecycleState.paused:
        _onPaused();
        break;
    }
  }

  void _onDetached() => print('detached');

  void _onResumed() {
    if (_shouldRequestPermission) {
      reInitializerButton();
      print('===== OnResumed');
      _shouldRequestPermission = false;
    }
  }

  void _onInactive() => print('inactive');

  void _onHidden() => print('hidden');

  void _onPaused() {
    print('===== OnPaused');
    _shouldRequestPermission = true;
  }

  Future<void> initialQueries() async {
    personList = await loadAllPersons();
    final count = personList.length;
    String? imageUrl;
    String? userId;
    UserProfile user = UserProfile();

    final isConnected = await networkInfo.isConnected;
    if (!isConnected) {
      CustomToast.showToast(LocaleKeys.face_control_errors_check_internet.tr());
    }

    if (isConnected) {
      try {
        // Get token and determine user role
        final token = storage.read(TOKEN);
        final isDemoMode = storage.read(is_demo) ?? false;

        String? userRole;
        String apiEndpoint;

        if (token != null) {
          // Regular authenticated user
          userId = JwtDecoder.getUserId(token);
          userRole = JwtDecoder.getUserRole(token);
        } else if (isDemoMode) {
          // Demo mode - extract user ID from guest token
          userId = JwtDecoder.getUserId(GUEST_TOKEN);
          userRole = JwtDecoder.getUserRole(GUEST_TOKEN);
        }

        if (userId == null || userRole == null) {
          CustomToast.showToast(LocaleKeys.face_control_errors_user_not_found.tr());
          return;
        }

        // Determine the correct API endpoint based on user role
        if (userRole == 'supervisor') {
          apiEndpoint =
              '${ApiPath.baseUrl}${ApiPath.supervisorProfilePath}/$userId';
        } else if (userRole == 'seller') {
          apiEndpoint =
              '${ApiPath.baseUrl}${ApiPath.sellerProfilePath}/$userId';
        } else {
          CustomToast.showToast('${LocaleKeys.face_control_errors_unknown_role.tr()}$userRole');
          return;
        }

        print('Fetching user profile from: $apiEndpoint');
        print('User role: $userRole, User ID: $userId');

        final response = await dio.get(apiEndpoint);
        if (response.statusCode == 200 && response.data != null) {
          user = UserProfile.fromJson(response.data);

          // Check if user data is valid
          if (user.isValid) {
            // Cache user profile using universal cache
            final cacheKey = userRole == 'supervisor'
                ? CacheKeys.supervisorProfile
                : CacheKeys.sellerProfile;
            await CacheHelper.cacheUserProfile<UserProfile>(
              cacheKey: cacheKey,
              profile: user,
              toJsonFunction: () => user.toJson(),
            );

            imageUrl = user.face;

            print('User image: $imageUrl');
            print('User model: ${user.toJson()}');
            print('User profile cached with key: $cacheKey');
          } else {
            print('Invalid user data received from API');
            CustomToast.showToast(LocaleKeys.face_control_errors_invalid_user_data.tr());
          }
        }
      } catch (e) {
        storage.write(server_approved, false);
        storage.write(local_approved, false);
        print(e);
        CustomToast.showToast('${LocaleKeys.face_control_errors_profile_load_error.tr()}$e');
      }
    }

    if (count == 0) {
      await _handleEmptyDatabase(userId ?? '', imageUrl);
    } else if (count == 1) {
      setState(() {
        hasAnyUser = true;
        serverApproved = imageUrl != null;
        storage.write(server_approved, serverApproved);
      });
    }
  }

  Future<void> _handleEmptyDatabase(String userId, String? imageUrl) async {
    if (imageUrl == null) {
      setState(() {
        serverApproved = false;
        _listLoading.value = false;
        storage.write(server_approved, serverApproved);
      });
      return;
    }

    try {
      setState(() {
        print('Loading: true');
        _listLoading.value = true;
        serverApproved = true;
        storage.write(server_approved, serverApproved);
      });

      final directory = await getApplicationDocumentsDirectory();
      final time = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      final fileName = imageUrl.split("/").last;
      final name = fileName.split('.').first;
      final ext = fileName.split('.').last;
      final filePath = '${directory.path}/${name}_$time.$ext';

      final response = await dio.download(
          '${AppEnvironment.baseImageUrl}$imageUrl', filePath);

      if (response.statusCode == 200) {
        final imageFile = File(filePath);
        await enrollPerson(
          personImage: imageFile,
          personId: userId,
          isFromCamera: false,
        );
      }
    } catch (e) {
      print(e);
      CustomToast.showToast(LocaleKeys.face_control_errors_download_error.tr());
    } finally {
      setState(() {
        print('Loading: false');
        _listLoading.value = false;
      });
    }
  }

  reInitializerButton() {
    setState(() {
      _initFuture = permissionWall();
    });
  }

  Future<void> initBioLock() async {
    int facePluginState = -1;
    String warningState = "";
    bool visibleWarning = false;

    try {
      if (Platform.isAndroid) {
        await _faceSdkPlugin
            .setActivation(AppStrings.androidFaceToken)
            .then((value) => facePluginState = value ?? -1);
      } else {
        await _faceSdkPlugin
            .setActivation(AppStrings.iOSFaceToken)
            .then((value) => facePluginState = value ?? -1);
      }

      if (facePluginState == 0) {
        await _faceSdkPlugin
            .init()
            .then((value) => facePluginState = value ?? -1);
      }
    } catch (e) {}

    List<Person> personList = await loadAllPersons();
    await SettingsPageState.initSettings();

    int? livenessLevel = storage.read("liveness_level") ?? 0;

    try {
      await _faceSdkPlugin
          .setParam({'check_liveness_level': livenessLevel ?? 0});
    } catch (e) {}

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;

    if (facePluginState == -1) {
      warningState = "Invalid license!";
      visibleWarning = true;
    } else if (facePluginState == -2) {
      warningState = "License expired!";
      visibleWarning = true;
    } else if (facePluginState == -3) {
      warningState = "Invalid license!";
      visibleWarning = true;
    } else if (facePluginState == -4) {
      warningState = "No activated!";
      visibleWarning = true;
    } else if (facePluginState == -5) {
      warningState = "Init error!";
      visibleWarning = true;
    }

    setState(() {
      _warningState = warningState;
      _visibleWarning = visibleWarning;
      this.personList = personList;
    });

    await initialQueries();
  }

  Future<void> insertPerson(Person person, String cachePath) async {
    // Get a reference to the database.
    final db = await createDB();

    // Insert the Dog into the correct table. You might also specify the
    // `conflictAlgorithm` to use in case the same dog is inserted twice.
    //
    // In this case, replace any previous data.
    await db.insert(
      'person',
      person.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    deleteFileFromInternalStorage(cachePath);

    setState(() {
      this.personList.add(person);
    });
  }

  Future<void> deleteAllPerson() async {
    final db = await createDB();
    await db.delete('person');

    setState(() {
      this.personList.clear();
    });

    Fluttertoast.showToast(
        msg: "All person deleted!",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  Future<void> deletePerson(index) async {
    // ignore: invalid_use_of_protected_member

    final db = await createDB();
    await db.delete('person',
        where: 'name=?', whereArgs: [this.personList[index].name]);

    // ignore: invalid_use_of_protected_member
    setState(() {
      this.personList.removeAt(index);
    });

    storage.remove(local_approved);
    storage.remove(server_approved);

    Fluttertoast.showToast(
        msg: "Person removed!",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  Future<int?> getPersonTableSize() async {
    final db = await createDB();

    // Get the size of the 'person' table
    List<Map<String, dynamic>> result =
        await db.rawQuery('SELECT COUNT(*) FROM person');
    int? size = Sqflite.firstIntValue(result);

    // Close the database
    await db.close();
    return size;
  }

  Future enrollPerson(
      {dynamic personImage,
      String? personId,
      required bool isFromCamera}) async {
    var verSDK = androidInfo?.version.sdkInt ?? 0;

    Map<Permission, PermissionStatus> statuses = await [
      Platform.isAndroid && verSDK < 33
          ? Permission.storage
          : Permission.photos,
      Permission.camera,
      Permission.location,
      Permission.notification,
    ].request();

    if (statuses[Platform.isAndroid && verSDK < 33
                ? Permission.storage
                : Permission.photos] ==
            PermissionStatus.granted &&
        statuses[Permission.camera] == PermissionStatus.granted &&
        statuses[Permission.notification] == PermissionStatus.granted &&
        statuses[Permission.location] == PermissionStatus.granted) {
      try {
        File? image;
        if (personImage == null) {
          // Directly open camera for face capture
          image = await _pickImage(ImageSource.camera);
        } else {
          image = personImage;
        }

        print("FUUUUUC<: ${image?.path}");

        if (image == null) return;

        final faces = await _faceSdkPlugin.extractFaces(image.path);

        if (faces.length == 1) {
          num randomNumber =
              10000 + Random().nextInt(10000); // from 0 upto 99 included
          Person person = Person(
              name: personId == null ? 'Person-[$randomNumber]' : personId,
              faceJpg: faces[0]['faceJpg'],
              templates: faces[0]['templates']);
          insertPerson(person, image.path);
        } else {
          deleteFileFromInternalStorage(image.path);
        }

        ///For many faces
        // for (var face in faces) {
        //   num randomNumber =
        //       10000 + Random().nextInt(10000); // from 0 upto 99 included
        //   Person person = Person(
        //       name: 'Person' + randomNumber.toString(),
        //       faceJpg: face['faceJpg'],
        //       templates: face['templates']);
        //   insertPerson(person);
        // }

        if (faces.length == 0) {
          Fluttertoast.showToast(
              msg: "Yuz aniqlanmadi",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0);
        } else if (faces.length == 1) {
          Fluttertoast.showToast(
              msg: "Yuz qo'shildi",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
              timeInSecForIosWeb: 1,
              backgroundColor: AppColors.cFirstColor,
              textColor: Colors.white,
              fontSize: 16.0);
        } else {
          Fluttertoast.showToast(
              msg: "Bittadan ortiq yuz",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0);
        }
      } catch (e, s) {
        print(e);
        CustomToast.showToast(e.toString() + s.toString());
      }
    } else {
      //CustomToast.showToast("Permission issue!");
      permissionWall();
    }
  }

  /// Pick image from camera only
  Future<File?> _pickImage(ImageSource source) async {
    try {
      final  picker = di<ImagePickerUtils>();

      ///Custom camera
      File pickedFile = File(await picker.selectImageFromCamera(
          this.context,
          previewMessage: LocaleKeys.face_control_camera_preview_message.tr(),
          isVideo: false,
          isHD: true,
          isFront: true));

        return pickedFile;

        // Process the image for face detection
        // await _processImageForFaceDetection(imageFile);

    } catch (e) {
      if (mounted) {
        CustomToast.showToast('${LocaleKeys.face_control_errors_image_capture_error.tr()}$e');
      }
    }
    return null;
  }

  /// Process the selected image for face detection
  Future<void> _processImageForFaceDetection(File imageFile) async {
    try {
      // Read image bytes
      final Uint8List imageBytes = await imageFile.readAsBytes();

      // Extract faces using the face SDK
      final faces = await _faceSdkPlugin.extractFaces(imageFile.path);

      if (faces.isEmpty) {
        Fluttertoast.showToast(
          msg: "Yuz aniqlanmadi",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0,
        );
      } else if (faces.length == 1) {
        // Single face detected - proceed with enrollment
        final face = faces.first;

        // Create person object
        final person = Person(
          name: 'User_${DateTime.now().millisecondsSinceEpoch}',
          faceJpg: imageBytes,
          templates: Uint8List.fromList([]), // Placeholder for templates
        );

        // Add to person list
        setState(() {
          personList.add(person);
        });

        Fluttertoast.showToast(
          msg: "Yuz qo'shildi",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: AppColors.cFirstColor,
          textColor: Colors.white,
          fontSize: 16.0,
        );

        // Update storage flags
        storage.write(local_approved, true);
      } else {
        Fluttertoast.showToast(
          msg: "Bittadan ortiq yuz",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0,
        );
      }
    } catch (e) {
      CustomToast.showToast('${LocaleKeys.face_control_errors_image_process_error.tr()}$e');
    }
  }

  Future<void> _logout() async {
    try {
      // Use LogoutService for complete logout with storage erasure
      final logoutService = di<LogoutService>();
      await logoutService.performCompleteLogout(
        showMessage: false, // We'll show our own message
        context: this.context,
      );

      if (mounted) {
        Navigator.of(this.context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginPage()),
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(this.context).showSnackBar(
          SnackBar(
            content: Text('${LocaleKeys.face_control_errors_logout_error.tr()}$e'),
            backgroundColor: AppColors.cReddishColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    ///These should be checked in every build!
    serverApproved = storage.read(server_approved) ?? false;
    localApproved = storage.read(local_approved) ?? false;

    if (personList.isNotEmpty) {
      hasAnyUser = true;
    } else {
      hasAnyUser = false;
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.cCardsColor,
        title: Text(LocaleKeys.face_control_title.tr(),
            style: TextStyle(
                fontSize: context.isTablet ? 12.sp : 16.sp,
                color: AppColors.white)),
        toolbarHeight: 70.h,
        centerTitle: true,
        leadingWidth: 40.w,
        iconTheme: IconThemeData(size: 22.h, color: AppColors.white),
        actions: [
          Padding(
            padding: EdgeInsets.only(right: context.isTablet ? 10.w : 0),
            child: IconButton(
                onPressed: () {
                  _logout();
                },
                icon: Icon(Icons.logout, size: 22.h)),
          ),
        ],
      ),
      body: FutureBuilder<dynamic>(
          future: _initFuture,
          builder: (context, snapshot) {
            switch (snapshot.connectionState) {
              // case ConnectionState.waiting:
              //   {
              //     // Otherwise, display a loading indicator.
              //     return Center(
              //         child: CupertinoActivityIndicator(
              //       color: Theme.of(context).primaryColor,
              //       radius: 30.r,
              //     ));
              //   }
              default:
                if (snapshot.hasError) {
                  print('Error: ${snapshot.connectionState}');
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          height: 300.h,
                          child: Column(
                            children: [
                              SizedBox(
                                height: 20.h,
                              ),
                              Expanded(
                                  child: SvgPicture.asset(
                                Assets.iconsWarning,
                                height: 140.h,
                              )),
                              Padding(
                                  padding: EdgeInsets.only(
                                      top: 10.h,
                                      left: 30.w,
                                      right: 30.w,
                                      bottom: 10.h),
                                  child: Text(
                                    LocaleKeys.face_control_please_refresh_page.tr(),
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        color: AppColors.cGrayDarkColor),
                                  )),
                              CupertinoButton(
                                  color:
                                      AppColors.cGrayDarkColor.withAlpha(80),
                                  onPressed: () {
                                    reInitializerButton();
                                  },
                                  child: Text(
                                    LocaleKeys.face_control_refresh.tr(),
                                    style: TextStyle(
                                        color: AppColors.cGrayDarkColor),
                                  )),
                              SizedBox(
                                height: 20.h,
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                } else {
                  print('DEFAULT: ${snapshot.connectionState}');
                  return Container(
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                    child: SingleChildScrollView(
                      child: Column(
                        children: <Widget>[
                          SizedBox(height: 20.h),
                          Card(
                              color: AppColors.cCardsColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(15.r),
                              ),
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    vertical: 8.h, horizontal: 10.w),
                                child: ListTile(
                                  leading: SvgPicture.asset(Assets.iconsDanger,
                                      color: Colors.red,
                                      height: 24.h),
                                  subtitle: Column(
                                    children: [
                                      Text(
                                        LocaleKeys.face_control_warning_message.tr(),
                                        style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                                      ),
                                      SizedBox(height: 14),
                                      Text(
                                        LocaleKeys.face_control_warning_details.tr(),
                                        style: TextStyle(fontSize: 13.sp),
                                      ),
                                    ],
                                  ),
                                ),
                              )),
                          SizedBox(height: 20.h),
// Replace this section in your build method
                        snapshot.connectionState == ConnectionState.waiting
                            ? Center(
                                child: Transform.scale(
                                  scale: 1.8,
                                  child: Lottie.asset(
                                    Assets.animationsLotiePulse,
                                    width: 170.w,
                                    height: 170.h,
                                    fit: BoxFit.cover,
                                    frameRate: FrameRate.max,
                                    repeat: true,
                                    addRepaintBoundary: true,
                                  ),
                                ),
                              )
                            : ZoomTapAnimation(
                                onTap: () {
                                  ///Refresh page if something happens
                                  reInitializerButton();
                                  CustomToast.showToast('😊');
                                },
                                child: SvgPicture.asset(Assets.iconsFaceIdBig,
                                        height: 170.h)
                                    .animate()
                                    .fadeIn(
                                        duration: 1000
                                            .ms) // uses `Animate.defaultDuration`
                                    .scale(
                                        curve: Curves.easeInSine,
                                        duration: 500.ms,
                                        begin: Offset(0.9, 0.9),
                                        end: Offset(1,
                                            1)) // inherits duration from fadeIn
                                    .shimmer(
                                        delay: 1000.ms,
                                        duration: 1500.ms,
                                        color: AppColors.cSecondaryColor)),
                        SizedBox(
                          height: 20.h,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 6.0),
                          child: Row(
                            children: <Widget>[
                              Visibility(
                                visible: !hasAnyUser,
                                child: Expanded(
                                  flex: 1,
                                  child: ElevatedButton.icon(
                                      label: Text(LocaleKeys.face_control_enter_face.tr(),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                              fontSize: context.isTablet
                                                  ? 12.sp
                                                  : 16.sp,
                                              color: AppColors.white)),
                                      icon: Padding(
                                        padding: EdgeInsets.only(left: 5.w),
                                        child: ValueListenableBuilder<bool>(
                                            valueListenable: _listLoading,
                                            builder: (ctx, value, _) {
                                              if (!value) {
                                                return Icon(Icons.person_add,
                                                    size: 22.h,
                                                    color: AppColors.white);
                                              } else {
                                                return Center(
                                                  child:
                                                      CupertinoActivityIndicator(
                                                          color: Theme.of(context)
                                                              .primaryColor),
                                                );
                                              }
                                            }),
                                      ),
                                      style: ElevatedButton.styleFrom(
                                          padding: EdgeInsets.only(
                                              top: 10.h, bottom: 10.h),
                                          // foregroundColor: Colors.white70,
                                          backgroundColor: AppColors.cFirstColor,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(12.r)),
                                          )),
                                      onPressed: !_listLoading.value
                                          ? () => enrollPerson(isFromCamera: true)
                                          : null),
                                ),
                              ),
                              Visibility(
                                  visible: !localApproved && !hasAnyUser,
                                  child: SizedBox(width: 20.w)),
                              Visibility(
                                visible: !localApproved && hasAnyUser ,
                                child: Expanded(
                                  flex: 1,
                                  child: ElevatedButton.icon(
                                      label: Text(LocaleKeys.face_control_confirm_face.tr(),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                              fontSize: context.isTablet
                                                  ? 12.sp
                                                  : 16.sp,
                                              color: AppColors.white)),
                                      icon: Padding(
                                        padding: EdgeInsets.only(left: 5.w),
                                        child: Icon(
                                          Icons.person_pin_sharp,
                                          size: 22.h,
                                          color: AppColors.white,
                                        ),
                                      ),
                                      style: ElevatedButton.styleFrom(
                                          padding: EdgeInsets.only(
                                              top: 10.h, bottom: 10.h),
                                          backgroundColor: AppColors.cFirstColor,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(12.r)),
                                          )),
                                      onPressed: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  FaceRecognitionView(
                                                    personList: this.personList,
                                                    server_approved:
                                                        serverApproved,
                                                  )),
                                        ).then((value) => reInitializerButton());
                                      }),
                                ),
                              ),
                            ],
                          ),
                        ),

                        ///For debugging
                        Visibility(
                          visible: false,
                          child: Padding(
                            padding: EdgeInsets.only(top: 6.h),
                            child: Row(
                              children: <Widget>[
                                Expanded(
                                  flex: 1,
                                  child: ElevatedButton.icon(
                                      label: Text(LocaleKeys.face_control_settings.tr()),
                                      icon: Icon(Icons.settings, size: 22.h),
                                      style: ElevatedButton.styleFrom(
                                          padding: EdgeInsets.only(
                                              top: 10.h, bottom: 10.h),
                                          backgroundColor:
                                              AppColors.cFirstColor,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(12.r)),
                                          )),
                                      onPressed: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  SettingsPage(
                                                    homePageState: this,
                                                  )),
                                        );
                                      }),
                                ),
                                SizedBox(width: 20.w),
                                Expanded(
                                  flex: 1,
                                  child: ElevatedButton.icon(
                                      label: Text(LocaleKeys.face_control_about.tr()),
                                      icon: Icon(Icons.info, size: 22.h),
                                      style: ElevatedButton.styleFrom(
                                          padding: EdgeInsets.only(
                                              top: 10.h, bottom: 10.h),
                                          backgroundColor:
                                              AppColors.cFirstColor,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(12.r)),
                                          )),
                                      onPressed: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  const AboutPage()),
                                        );
                                      }),
                                ),
                              ],
                            ),
                          ),
                        ),
                          SizedBox(height: 8.h),

                          Container(
                            height: 400.h, // Fixed height instead of Expanded
                            child: Stack(
                              children: [
                                ValueListenableBuilder<bool>(
                                    valueListenable: _listLoading,
                                    builder: (ctx, value, _) {
                                      if (!value) {
                                        return PersonView(
                                          personList: this.personList,
                                          homePageState: this,
                                          isServerApproved: serverApproved,
                                          isLocalApproved: localApproved,
                                          personDeleted: () {
                                            storage.write(local_approved, false);
                                            setState(() {
                                              localApproved = false;
                                            });
                                          },
                                        );
                                      } else {
                                        return Center(
                                          child: CupertinoActivityIndicator(
                                              color: AppColors.cFirstColor,
                                              radius: 30.r),
                                        );
                                      }
                                    }),
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    Visibility(
                                        visible: _visibleWarning,
                                        child: Container(
                                          width: double.infinity,
                                          height: 40.h,
                                          color: Colors.redAccent,
                                          child: Center(
                                            child: Text(
                                              _warningState,
                                              textAlign: TextAlign.center,
                                              style: TextStyle(fontSize: 20.sp),
                                            ),
                                          ),
                                        ))
                                  ],
                                )
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 4.h,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Flexible(
                                child: SvgPicture.asset(
                                  Assets.iconsCompanyLogo,
                                  color: Theme.of(context).iconTheme.color,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 20.h),
                        ],
                      ),
                    ),
                  );
                }
            }
          }),
    );
  }
}

Future<Database> createDB() async {
  final database = openDatabase(
    // Set the path to the database. Note: Using the `join` function from the
    // `path` package is best practice to ensure the path is correctly
    // constructed for each platform.
    join(await getDatabasesPath(), 'person.db'),
    // When the database is first created, create a table to store dogs.
    onCreate: (db, version) {
      // Run the CREATE TABLE statement on the database.
      return db.execute(
        'CREATE TABLE person(name text, faceJpg blob, templates blob)',
      );
    },
    // Set the version. This executes the onCreate function and provides a
    // path to perform database upgrades and downgrades.
    version: 1,
  );

  return database;
}

// A method that retrieves all the dogs from the dogs table.
Future<List<Person>> loadAllPersons() async {
  // Get a reference to the database.
  final db = await createDB();

  // Query the table for all The Dogs.
  final List<Map<String, dynamic>> maps = await db.query('person', limit: 1);

  // Convert the List<Map<String, dynamic> into a List<Dog>.
  return List.generate(maps.length, (i) {
    return Person.fromMap(maps[i]);
  });
}
