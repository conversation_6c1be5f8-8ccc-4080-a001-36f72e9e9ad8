import 'dart:typed_data';

import 'package:easy_localization/easy_localization.dart';
import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_storage/get_storage.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/core/widgets/custom_toast.dart';
import 'package:click_bazaar/di/dependency_injection.dart' as di;
import 'package:click_bazaar/core/extensions/context_extensions.dart';
import 'package:click_bazaar/core/theme/app_colors.dart';

// import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'person.dart';
import 'bio_lock_page.dart';

// ignore: must_be_immutable
class PersonView extends StatefulWidget {
  final List<Person> personList;
  final BioLockPageState homePageState;
  final bool isServerApproved;
  final bool isLocalApproved;
  final VoidCallback personDeleted;

  const PersonView(
      {super.key,
      required this.personList,
      required this.personDeleted,
      required this.homePageState,
      required this.isServerApproved,
      required this.isLocalApproved});

  @override
  _PersonViewState createState() => _PersonViewState();
}

class _PersonViewState extends State<PersonView> {
  final GetStorage storage = di.di();

  deletePerson(int index) async {
    await widget.homePageState.deletePerson(index);
    widget.homePageState.setState(() {
      print('Person deleted');
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
        physics: BouncingScrollPhysics(),
        itemCount: widget.personList.length,
        itemBuilder: (BuildContext context, int index) {
          return SizedBox(
              child: Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15.r),
                  ),
                  color: widget.isLocalApproved
                      ? AppColors.cCardsColor
                      : AppColors.cReddishColor,
                  child: Container(
                    padding: EdgeInsets.all(10.h),
                    child: Row(
                      children: [
                        Expanded(
                          flex: context.isTablet ? 1 : 2,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(15.r),
                            child: SizedBox(
                              child: FaceImageWidget(
                                  imageData: widget
                                      .homePageState.personList[index].faceJpg),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            widget.isLocalApproved
                                ? "Tasdiqlangan"
                                : "Tasdiqlanmagan",
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            style: TextStyle(
                                color: AppColors.white, fontSize: 18.sp),
                          ),
                        ),
                        Visibility(
                          visible: !widget.isServerApproved,
                          child: SizedBox(
                            width: 5.w,
                          ),
                        ),
                        Expanded(
                            flex: 1,
                            child: !widget.isServerApproved
                                ? IconButton(
                                    icon: Icon(Icons.delete,
                                        color: AppColors.white, size: 22.h),
                                    onPressed: () {
                                      deletePerson(index);
                                      widget.personDeleted;
                                    })
                                : IconButton(
                                    icon: !widget.isServerApproved?SvgPicture.asset(
                                        Assets.iconsTickCircle,
                                        height: 40.h):SvgPicture.asset(
                                        Assets.iconsInfoCircle,
                                        color: AppColors.white,
                                        height: 40.h),
                                    onPressed: () => CustomToast.showToast(
                                        "Rasmni faqat admin o'chira oladi.."),
                                  )),
                        SizedBox(
                          width: 8.w,
                        )
                      ],
                    ),
                  )));
        });
  }
}

// Separate stateless widget just for the image to better control repaints
class FaceImageWidget extends StatelessWidget {
  final Uint8List imageData;

  const FaceImageWidget({
    Key? key,
    required this.imageData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15.r),
        child: SizedBox(
          height: 110.h,
          width: double.infinity,
          // Using FadeInImage with MemoryImage instead of Image.memory
          child: FutureBuilder<void>(
            // Create a small delay to ensure proper loading
            future: Future.delayed(const Duration(milliseconds: 50)),
            builder: (context, snapshot) {
              if (snapshot.connectionState != ConnectionState.done) {
                return Center(
                  child: SizedBox(
                    height: 45.h,
                    width: 45.h,
                    child: CupertinoActivityIndicator(
                      radius: 10.r,
                    ),
                  ),
                );
              }

              // Using a memory network image with explicit decoration
              return Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: MemoryImage(imageData),
                    fit: BoxFit.cover,
                  ),
                ),
                child: const SizedBox.expand(),
              );
            },
          ),
        ),
      ),
    );
  }
}
